package com.github.b4ndithelps.titlefix.mixin;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Gui;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Title Fix Mixin - 修复标题覆盖层在退出世界时不清除的问题
 * 当退出世界或服务器时，清除游戏内标题覆盖层，防止传递到下一个世界
 * 
 * Title Fix Mixin - Fixes title overlay not clearing when exiting worlds
 * When exiting a world or server, clears the in-game title overlay to prevent transferring to the next world
 */
@Mixin(Minecraft.class)
public class TitleFixMixin {
    
    @Shadow
    private volatile boolean running;
    
    /**
     * 在清除世界时清除标题覆盖层
     * Clear title overlay when clearing level
     */
    @Inject(method = "clearLevel()V", at = @At("HEAD"))
    private void clearTitleOnClearLevel(CallbackInfo ci) {
        clearTitleOverlay();
    }

    /**
     * 在清除世界时清除标题覆盖层（带参数版本）
     * Clear title overlay when clearing level (with parameter version)
     */
    @Inject(method = "clearLevel(Lnet/minecraft/client/gui/screens/Screen;)V", at = @At("HEAD"))
    private void clearTitleOnClearLevelWithScreen(Screen screen, CallbackInfo ci) {
        clearTitleOverlay();
    }



    /**
     * 清除标题覆盖层的通用方法
     * Common method to clear title overlay
     */
    private void clearTitleOverlay() {
        try {
            Minecraft minecraft = (Minecraft)(Object)this;

            // 清除标题覆盖层
            // Clear title overlay
            if (minecraft.gui != null) {
                // 使用我们的Mixin方法直接清除
                ((GuiTitleFixMixin)(Object)minecraft.gui).clearAllTitles();

                // 同时使用原版方法作为备用
                minecraft.gui.setTitle(Component.empty());
                minecraft.gui.setSubtitle(Component.empty());
                minecraft.gui.setTimes(0, 0, 0);
            }
        } catch (Exception e) {
            // 记录错误但不崩溃游戏
            // Log error but don't crash the game
            System.err.println("Error in TitleFixMixin: " + e.getMessage());
        }
    }
}
