package com.github.b4ndithelps.titlefix;

import com.github.b4ndithelps.titlefix.handler.LowMagisteelPickaxeHandler;
import com.mojang.logging.LogUtils;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;

// 这里的值应该与META-INF/mods.toml文件中的条目匹配
@Mod(TltleFix.MODID)
public class TltleFix {

    // 定义模组ID，供所有地方引用
    public static final String MODID = "tltlefix";
    // 直接引用slf4j日志记录器
    public static final Logger LOGGER = LogUtils.getLogger();

    public TltleFix() {
    }

    private void commonSetup(final FMLCommonSetupEvent event) {

    }

    // 你可以使用SubscribeEvent让事件总线发现要调用的方法
    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event) {

    }

    // 客户端设置已移除，因为只需要服务端的镐子挖掘修复功能
}
