package com.github.b4ndithelps.tensuraenigmatic.items;


import com.github.b4ndithelps.tensuraenigmatic.config.TensuraEnigmaticConfig;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticTabs;
import com.github.manasmods.manascore.api.attribute.AttributeModifierHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import net.minecraft.ChatFormatting;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.level.Level;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import top.theillusivec4.curios.api.SlotContext;
import top.theillusivec4.curios.api.type.capability.ICurioItem;

import javax.annotation.Nullable;
import java.util.List;
import java.util.UUID;

/**
 * 魔源之戒 - 提升魔素上限1000点的饰品
 * 每个玩家只能从地牢宝箱中获得一次
 */
public class RingOfArcaneSource extends net.minecraft.world.item.Item implements ICurioItem {
    
    // 魔素加成的唯一UUID
    private static final UUID MAGICULE_MODIFIER_UUID = UUID.fromString("a1b2c3d4-5678-9abc-def0-123456789abc");
    
    // 魔素加成数值 - 从配置文件获取
    public static double getMagiculeBonus() {
        return TensuraEnigmaticConfig.RING_OF_ARCANE_SOURCE_MAGICULE_BONUS.get().doubleValue();
    }
    
    public RingOfArcaneSource() {
        super(new Properties()
                .stacksTo(1)
                .rarity(Rarity.RARE)
                .fireResistant()
                .tab(TensuraEnigmaticTabs.TENSURA_ENIGMATIC_TAB));
    }
    
    @Override
    @OnlyIn(Dist.CLIENT)
    public void appendHoverText(ItemStack stack, @Nullable Level worldIn, List<Component> list, TooltipFlag flagIn) {
        // 添加描述文本（金色，始终显示）
        list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_arcane_source.description").withStyle(ChatFormatting.GOLD));

        // 当按住Shift时显示详细信息
        if (Screen.hasShiftDown()) {
            // 添加被动效果标题（紫色）
            list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_arcane_source.passive_effects").withStyle(ChatFormatting.LIGHT_PURPLE));

            // 添加魔素提升效果（深紫色）
            list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_arcane_source.magicule_bonus",
                String.valueOf((int)getMagiculeBonus())).withStyle(ChatFormatting.DARK_PURPLE));
        } else {
            // 显示按住Shift查看详情的提示（使用EnigmaticLegacy的键）
            list.add(Component.translatable("tooltip.enigmaticlegacy.holdShift").withStyle(ChatFormatting.GRAY));
        }
    }
    
    @Override
    public Multimap<Attribute, AttributeModifier> getAttributeModifiers(SlotContext slotContext, UUID uuid, ItemStack stack) {
        Multimap<Attribute, AttributeModifier> attributes = HashMultimap.create();
        
        // 添加魔素上限加成
        attributes.put(TensuraAttributeRegistry.MAX_MAGICULE.get(),
            new AttributeModifier(MAGICULE_MODIFIER_UUID,
                "tensuraenigmatic:ring_of_arcane_source_magicule_bonus",
                getMagiculeBonus(),
                AttributeModifier.Operation.ADDITION));
        
        return attributes;
    }
    
    @Override
    public List<Component> getAttributesTooltip(List<Component> tooltips, ItemStack stack) {
        // 清除默认的属性工具提示，我们使用自定义的
        tooltips.clear();
        return tooltips;
    }
    
    @Override
    public boolean canEquipFromUse(SlotContext slotContext, ItemStack stack) {
        return true;
    }

    @Override
    public boolean canEquip(SlotContext slotContext, ItemStack stack) {
        return true;
    }

    @Override
    public void playRightClickEquipSound(net.minecraft.world.entity.LivingEntity entity, ItemStack stack) {
        // 播放装备声音
        entity.playSound(net.minecraft.sounds.SoundEvents.ARMOR_EQUIP_GOLD, 1.0F, 1.0F);
    }


}
