package com.github.b4ndithelps.tensuraenigmatic.items;

import com.github.b4ndithelps.tensuraenigmatic.config.TensuraEnigmaticConfig;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticTabs;
import net.minecraft.ChatFormatting;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.level.Level;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import top.theillusivec4.curios.api.SlotContext;
import top.theillusivec4.curios.api.type.capability.ICurioItem;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 魔晶之戒 - 每秒恢复魔素的饰品
 */
public class ManaRing extends net.minecraft.world.item.Item implements ICurioItem {
    
    // 魔素恢复数值 - 从配置文件获取
    public static int getManaRecovery() {
        return TensuraEnigmaticConfig.MANA_RING_RECOVERY_AMOUNT.get();
    }

    public static int getRecoveryInterval() {
        return TensuraEnigmaticConfig.MANA_RING_RECOVERY_INTERVAL.get();
    }
    
    public ManaRing() {
        super(new Properties()
                .stacksTo(1)
                .rarity(Rarity.UNCOMMON)
                .fireResistant()
                .tab(TensuraEnigmaticTabs.TENSURA_ENIGMATIC_TAB));
    }
    
    @Override
    @OnlyIn(Dist.CLIENT)
    public void appendHoverText(ItemStack stack, @Nullable Level worldIn, List<Component> list, TooltipFlag flagIn) {
        // 添加空行（在栏位信息后）
        list.add(Component.literal(""));
        
        // 当按住Shift时显示详细信息
        if (Screen.hasShiftDown()) {
            // 添加被动效果标题（紫色）
            list.add(Component.translatable("tooltip.tensuraenigmatic.mana_ring.passive_effects").withStyle(ChatFormatting.LIGHT_PURPLE));
            
            // 添加魔素恢复效果（深紫色）
            list.add(Component.translatable("tooltip.tensuraenigmatic.mana_ring.mana_recovery",
                String.valueOf(getManaRecovery())).withStyle(ChatFormatting.DARK_PURPLE));
        } else {
            // 显示按住Shift查看详情的提示（使用EnigmaticLegacy的键）
            list.add(Component.translatable("tooltip.enigmaticlegacy.holdShift").withStyle(ChatFormatting.GRAY));
        }
    }
    
    @Override
    public boolean canEquipFromUse(SlotContext slotContext, ItemStack stack) {
        return true;
    }
    
    @Override
    public boolean canEquip(SlotContext slotContext, ItemStack stack) {
        return true;
    }
    
    @Override
    public void playRightClickEquipSound(net.minecraft.world.entity.LivingEntity entity, ItemStack stack) {
        // 播放装备声音
        entity.playSound(net.minecraft.sounds.SoundEvents.ARMOR_EQUIP_GOLD, 1.0F, 1.0F);
    }


}
