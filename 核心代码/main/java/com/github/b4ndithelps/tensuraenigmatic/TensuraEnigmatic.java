package com.github.b4ndithelps.tensuraenigmatic;

import com.github.b4ndithelps.tensuraenigmatic.config.TensuraEnigmaticConfig;
import com.github.b4ndithelps.tensuraenigmatic.handler.CursedItemTooltipHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.CursedRingEnhancementHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.CursedRingTooltipHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.ElfPrayerHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.LootTableHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.LowMagisteelEnhancementHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.ManaRingHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.RingOfTheElvesHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.TheCubeEnhancementHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.TheCubeTooltipHandler;
import com.github.b4ndithelps.tensuraenigmatic.handler.ToolTierOverrideHandler;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticItems;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticTabs;
import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;

// 这里的值应该与META-INF/mods.toml文件中的条目匹配
@Mod(TensuraEnigmatic.MODID)
public class TensuraEnigmatic {

    // 定义模组ID，供所有地方引用
    public static final String MODID = "tensuraenigmatic";
    // 直接引用slf4j日志记录器
    public static final Logger LOGGER = LogUtils.getLogger();

    public TensuraEnigmatic() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();
        modEventBus.addListener(this::commonSetup);

        // 注册配置
        TensuraEnigmaticConfig.register();

        // 注册物品
        TensuraEnigmaticItems.register(modEventBus);

        // 注册事件处理器
        MinecraftForge.EVENT_BUS.register(CursedItemTooltipHandler.class);
        MinecraftForge.EVENT_BUS.register(CursedRingEnhancementHandler.class);
        MinecraftForge.EVENT_BUS.register(CursedRingTooltipHandler.class);
        MinecraftForge.EVENT_BUS.register(ElfPrayerHandler.class);
        MinecraftForge.EVENT_BUS.register(LowMagisteelEnhancementHandler.class);
        MinecraftForge.EVENT_BUS.register(ManaRingHandler.class);
        MinecraftForge.EVENT_BUS.register(TheCubeEnhancementHandler.class);
        MinecraftForge.EVENT_BUS.register(TheCubeTooltipHandler.class);
        MinecraftForge.EVENT_BUS.register(RingOfTheElvesHandler.class);
        MinecraftForge.EVENT_BUS.register(ToolTierOverrideHandler.class);
        MinecraftForge.EVENT_BUS.register(LootTableHandler.class);
        MinecraftForge.EVENT_BUS.register(this);
        LOGGER.info("神秘遗物附属加载成功了哟~AQZL");
    }

    private void commonSetup(final FMLCommonSetupEvent event) {

    }

    // 你可以使用SubscribeEvent让事件总线发现要调用的方法
    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event) {

    }

    // 你可以使用EventBusSubscriber自动注册类中所有带@SubscribeEvent注解的静态方法
    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
    public static class ClientModEvents {

        @SubscribeEvent
        public static void onClientSetup(FMLClientSetupEvent event) {
            // 一些客户端设置代码
            LOGGER.info("客户端设置完成");
            LOGGER.info("玩家名称 >> {}", Minecraft.getInstance().getUser().getName());
        }
    }
}
