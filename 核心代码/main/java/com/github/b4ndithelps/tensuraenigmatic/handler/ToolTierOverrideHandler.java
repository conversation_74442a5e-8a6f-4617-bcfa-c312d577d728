package com.github.b4ndithelps.tensuraenigmatic.handler;

import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 覆盖工具等级检查，让低级魔钢镐能够挖掘所有方块
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class ToolTierOverrideHandler {
    
    /**
     * 处理工具正确性检查事件
     */
    @SubscribeEvent
    public static void onHarvestCheck(PlayerEvent.HarvestCheck event) {
        ItemStack tool = event.getEntity().getMainHandItem();
        BlockState blockState = event.getTargetBlock();
        
        // 检查是否是低级魔钢镐
        if (isLowMagisteelPickaxe(tool)) {
            // 检查是否是镐子可以挖掘的方块
            if (blockState.is(net.minecraft.tags.BlockTags.MINEABLE_WITH_PICKAXE)) {
                // 强制设置为可以正确挖掘
                event.setCanHarvest(true);
            }
        }
    }
    
    /**
     * 检查是否是低级魔钢镐
     */
    private static boolean isLowMagisteelPickaxe(ItemStack stack) {
        if (stack.isEmpty()) return false;
        
        // 检查物品的注册名
        String itemName = stack.getItem().toString();
        return itemName.contains("low_magisteel_pickaxe");
    }
}
