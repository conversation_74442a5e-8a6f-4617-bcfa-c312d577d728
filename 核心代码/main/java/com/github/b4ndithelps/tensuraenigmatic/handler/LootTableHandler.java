package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.github.b4ndithelps.tensuraenigmatic.config.TensuraEnigmaticConfig;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticItems;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.storage.loot.LootPool;
import net.minecraft.world.level.storage.loot.entries.LootItem;
import net.minecraft.world.level.storage.loot.functions.SetItemCountFunction;
import net.minecraft.world.level.storage.loot.predicates.LootItemRandomChanceCondition;
import net.minecraft.world.level.storage.loot.providers.number.ConstantValue;
import net.minecraftforge.event.LootTableLoadEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 处理战利品表，将魔源之戒添加到各种地牢宝箱中
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class LootTableHandler {
    
    /**
     * 当战利品表加载时，添加魔源之戒到地牢宝箱
     */
    @SubscribeEvent
    public static void onLootTableLoad(LootTableLoadEvent event) {
        ResourceLocation name = event.getName();
        
        // 检查是否是地牢相关的宝箱
        if (isDungeonChest(name)) {
            // 创建魔源之戒的战利品池
            LootPool.Builder poolBuilder = LootPool.lootPool()
                .setRolls(ConstantValue.exactly(1))
                .when(LootItemRandomChanceCondition.randomChance(TensuraEnigmaticConfig.RING_OF_ARCANE_SOURCE_DROP_CHANCE.get().floatValue()))
                .add(LootItem.lootTableItem(TensuraEnigmaticItems.RING_OF_ARCANE_SOURCE.get())
                    .apply(SetItemCountFunction.setCount(ConstantValue.exactly(1))));
            
            // 添加到战利品表
            event.getTable().addPool(poolBuilder.build());
        }
    }
    
    /**
     * 检查是否是地牢宝箱的战利品表
     */
    private static boolean isDungeonChest(ResourceLocation name) {
        String path = name.getPath();
        
        // 原版地牢宝箱
        if (path.contains("simple_dungeon") || 
            path.contains("abandoned_mineshaft") ||
            path.contains("stronghold") ||
            path.contains("desert_pyramid") ||
            path.contains("jungle_temple") ||
            path.contains("woodland_mansion") ||
            path.contains("pillager_outpost") ||
            path.contains("buried_treasure") ||
            path.contains("shipwreck") ||
            path.contains("underwater_ruin") ||
            path.contains("ruined_portal") ||
            path.contains("bastion") ||
            path.contains("nether_bridge") ||
            path.contains("end_city")) {
            return true;
        }
        
        // 模组地牢宝箱（通用匹配）
        if (path.contains("dungeon") ||
            path.contains("chest") ||
            path.contains("treasure") ||
            path.contains("loot")) {
            return true;
        }
        
        return false;
    }
}
