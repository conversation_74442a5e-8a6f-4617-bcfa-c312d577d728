package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.aizistral.enigmaticlegacy.api.items.ICursed;
import com.aizistral.enigmaticlegacy.handlers.SuperpositionHandler;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.contents.TranslatableContents;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.event.entity.player.ItemTooltipEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 处理被诅咒物品的工具提示
 * 对于非承受七咒之人，混淆工具提示文本
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class CursedItemTooltipHandler {

    @SubscribeEvent
    public static void onItemTooltip(ItemTooltipEvent event) {
        // 检查是否是被诅咒的物品
        if (event.getItemStack().getItem() instanceof ICursed) {
            // 检查玩家是否存在，如果不存在则跳过处理
            if (event.getEntity() == null) {
                return;
            }

            // 如果玩家不是承受七咒之人，混淆工具提示
            if (!SuperpositionHandler.isTheCursedOne(event.getEntity())) {
                event.getToolTip().replaceAll(component -> {
                    // 保留特定的工具提示不被混淆
                    if (component.getContents() instanceof TranslatableContents loc) {
                        if (loc.getKey().startsWith("tooltip.enigmaticlegacy.cursedOnesOnly"))
                            return component;
                    }

                    // 混淆其他文本
                    return Component.literal(SuperpositionHandler.obscureString(component.getString()))
                            .withStyle(component.getStyle());
                });
            }
        }
    }
}
