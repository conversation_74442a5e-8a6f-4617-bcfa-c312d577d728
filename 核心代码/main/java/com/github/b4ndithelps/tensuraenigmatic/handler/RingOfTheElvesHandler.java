package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.github.b4ndithelps.tensuraenigmatic.items.RingOfTheElves;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticItems;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import top.theillusivec4.curios.api.CuriosApi;
import top.theillusivec4.curios.api.type.capability.ICuriosItemHandler;
import top.theillusivec4.curios.api.type.inventory.ICurioStacksHandler;
import top.theillusivec4.curios.api.type.inventory.IDynamicStackHandler;

/**
 * 处理精灵之戒的自动附魔保护4效果
 * 当玩家装备精灵之戒时，所有穿戴的装备自动获得保护4附魔
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class RingOfTheElvesHandler {
    
    // 保护等级从配置文件获取
    private static int getProtectionLevel() {
        return RingOfTheElves.getProtectionLevel();
    }
    
    /**
     * 处理玩家tick事件，为装备精灵之戒的玩家的装备添加保护4附魔
     */
    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
        // 只在服务端处理，每20tick（1秒）执行一次
        if (event.side.isServer() && event.phase == TickEvent.Phase.END &&
            event.player != null && event.player.tickCount % 20 == 0) {

            Player player = event.player;

            try {
                // 检查玩家是否装备了精灵之戒
                if (hasRingOfTheElves(player)) {
                    applyProtectionToArmor(player);
                } else {
                    removeProtectionFromArmor(player);
                }
            } catch (Exception e) {
                // 记录错误但不崩溃游戏
                System.err.println("Error in RingOfTheElvesHandler: " + e.getMessage());
            }
        }
    }
    
    /**
     * 为玩家的装备添加保护4附魔
     */
    private static void applyProtectionToArmor(Player player) {
        // 检查所有装备槽位
        EquipmentSlot[] armorSlots = {
            EquipmentSlot.HEAD, EquipmentSlot.CHEST, 
            EquipmentSlot.LEGS, EquipmentSlot.FEET
        };
        
        for (EquipmentSlot slot : armorSlots) {
            ItemStack armorStack = player.getItemBySlot(slot);
            
            // 检查是否是装备且不为空
            if (!armorStack.isEmpty() && armorStack.getItem() instanceof ArmorItem) {
                // 检查是否已经有保护附魔
                int currentProtectionLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.ALL_DAMAGE_PROTECTION, armorStack);
                
                // 如果没有保护附魔或者等级低于配置值，则添加保护附魔
                if (currentProtectionLevel < getProtectionLevel()) {
                    // 添加保护附魔
                    armorStack.enchant(Enchantments.ALL_DAMAGE_PROTECTION, getProtectionLevel());
                }
            }
        }
    }
    
    /**
     * 从玩家的装备中移除由精灵之戒添加的保护4附魔
     * 注意：这个方法比较复杂，因为我们需要区分原有的附魔和戒指添加的附魔
     * 为了简化，我们只在玩家没有装备精灵之戒时检查是否需要移除保护4
     */
    private static void removeProtectionFromArmor(Player player) {
        // 检查所有装备槽位
        EquipmentSlot[] armorSlots = {
            EquipmentSlot.HEAD, EquipmentSlot.CHEST, 
            EquipmentSlot.LEGS, EquipmentSlot.FEET
        };
        
        for (EquipmentSlot slot : armorSlots) {
            ItemStack armorStack = player.getItemBySlot(slot);
            
            // 检查是否是装备且不为空
            if (!armorStack.isEmpty() && armorStack.getItem() instanceof ArmorItem) {
                // 检查是否有保护4附魔
                int currentProtectionLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.ALL_DAMAGE_PROTECTION, armorStack);
                
                // 如果有保护4附魔，我们需要检查这是否是由戒指添加的
                // 为了简化实现，我们在这里不移除附魔，让玩家保留已有的附魔
                // 这样可以避免意外移除玩家原本就有的附魔
            }
        }
    }
    
    /**
     * 检查玩家是否装备了精灵之戒
     */
    private static boolean hasRingOfTheElves(Player player) {
        try {
            ICuriosItemHandler handler = CuriosApi.getCuriosHelper().getCuriosHandler(player).orElse(null);

            if (handler != null) {
                for (ICurioStacksHandler stacksHandler : handler.getCurios().values()) {
                    if (stacksHandler != null) {
                        IDynamicStackHandler stackHandler = stacksHandler.getStacks();

                        if (stackHandler != null) {
                            for (int i = 0; i < stackHandler.getSlots(); i++) {
                                ItemStack stack = stackHandler.getStackInSlot(i);
                                if (stack != null && !stack.isEmpty() &&
                                    stack.getItem() == TensuraEnigmaticItems.RING_OF_THE_ELVES.get()) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 如果检查失败，返回false
            System.err.println("Error checking for Ring of the Elves: " + e.getMessage());
        }

        return false;
    }
}
