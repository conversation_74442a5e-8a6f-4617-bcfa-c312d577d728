package com.github.b4ndithelps.tensuraenigmatic.handler;

import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.Event;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 处理低级魔钢镐的挖掘增强
 * 让低级魔钢镐拥有和下界合金镐一样的挖掘强度
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class LowMagisteelEnhancementHandler {

    /**
     * 处理方块破坏速度事件，增强低级魔钢镐的挖掘速度
     */
    @SubscribeEvent
    public static void onBreakSpeed(PlayerEvent.BreakSpeed event) {
        ItemStack tool = event.getEntity().getMainHandItem();
        BlockState blockState = event.getState();

        // 检查是否是低级魔钢镐
        if (isLowMagisteelPickaxe(tool)) {
            // 检查是否是镐子可以挖掘的方块
            if (blockState.is(net.minecraft.tags.BlockTags.MINEABLE_WITH_PICKAXE)) {
                // 对于所有镐子可挖掘的方块，都给予下界合金镐的挖掘速度
                float enhancedSpeed = getEnhancedMiningSpeed(blockState, event.getOriginalSpeed());
                event.setNewSpeed(enhancedSpeed);
            }
        }
    }

    /**
     * 处理方块破坏事件，强制允许低级魔钢镐破坏高级方块
     */
    @SubscribeEvent
    public static void onBlockBreak(BlockEvent.BreakEvent event) {
        if (event.getPlayer() instanceof ServerPlayer player) {
            ItemStack tool = player.getMainHandItem();
            BlockState blockState = event.getState();

            // 检查是否是低级魔钢镐
            if (isLowMagisteelPickaxe(tool)) {
                // 检查是否是镐子可以挖掘的方块
                if (blockState.is(net.minecraft.tags.BlockTags.MINEABLE_WITH_PICKAXE)) {
                    // 如果原本不能正确挖掘，我们强制允许
                    if (blockState.requiresCorrectToolForDrops() && !tool.isCorrectToolForDrops(blockState)) {
                        // 强制设置为允许破坏
                        event.setResult(Event.Result.ALLOW);

                        // 手动处理掉落物
                        handleCustomDrops(player, blockState, event.getPos(), tool);
                    }
                }
            }
        }
    }

    /**
     * 手动处理掉落物
     */
    private static void handleCustomDrops(ServerPlayer player, BlockState blockState, BlockPos pos, ItemStack tool) {
        try {
            // 获取方块的掉落物
            var drops = blockState.getDrops(new net.minecraft.world.level.storage.loot.LootContext.Builder(player.getLevel())
                .withParameter(net.minecraft.world.level.storage.loot.parameters.LootContextParams.ORIGIN,
                    net.minecraft.world.phys.Vec3.atCenterOf(pos))
                .withParameter(net.minecraft.world.level.storage.loot.parameters.LootContextParams.TOOL, tool)
                .withOptionalParameter(net.minecraft.world.level.storage.loot.parameters.LootContextParams.THIS_ENTITY, player)
                .withOptionalParameter(net.minecraft.world.level.storage.loot.parameters.LootContextParams.BLOCK_ENTITY,
                    player.getLevel().getBlockEntity(pos)));

            // 掉落物品
            for (ItemStack drop : drops) {
                net.minecraft.world.entity.item.ItemEntity itemEntity =
                    new net.minecraft.world.entity.item.ItemEntity(player.getLevel(),
                        pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5, drop);
                player.getLevel().addFreshEntity(itemEntity);
            }
        } catch (Exception e) {
            // 如果出错，记录但不崩溃
            System.err.println("Error handling custom drops for low magisteel pickaxe: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否是低级魔钢镐
     */
    private static boolean isLowMagisteelPickaxe(ItemStack stack) {
        if (stack.isEmpty()) return false;
        
        // 检查物品的注册名
        String itemName = stack.getItem().toString();
        return itemName.contains("low_magisteel_pickaxe");
    }
    
    /**
     * 获取增强后的挖掘速度
     */
    private static float getEnhancedMiningSpeed(BlockState blockState, float originalSpeed) {
        // 如果原始速度已经很快，保持原速度
        if (originalSpeed > 15.0f) {
            return originalSpeed;
        }

        // 根据方块类型返回适当的挖掘速度
        // 这里设置为下界合金镐的挖掘速度

        if (blockState.is(net.minecraft.tags.BlockTags.NEEDS_DIAMOND_TOOL)) {
            return Math.max(originalSpeed, 9.0f); // 下界合金镐对钻石级方块的速度
        } else if (blockState.is(net.minecraft.tags.BlockTags.NEEDS_IRON_TOOL)) {
            return Math.max(originalSpeed, 12.0f); // 对铁级方块的速度
        } else if (blockState.is(net.minecraft.tags.BlockTags.NEEDS_STONE_TOOL)) {
            return Math.max(originalSpeed, 15.0f); // 对石级方块的速度
        }

        return Math.max(originalSpeed, 8.0f); // 默认速度
    }
}
