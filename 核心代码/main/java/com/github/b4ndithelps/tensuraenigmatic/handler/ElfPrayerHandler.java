package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticItems;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 处理精灵祈祷事件，在第一次精灵祈祷时给予精灵之戒
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class ElfPrayerHandler {
    
    private static final String NBT_KEY_FIRST_PRAYER = "tensuraenigmatic_first_elf_prayer";
    private static final String NBT_KEY_RING_RECEIVED = "tensuraenigmatic_elf_ring_received";
    
    /**
     * 监听玩家tick事件，检查是否完成了第一次精灵祈祷
     */
    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
        // 只在服务端处理，每20tick（1秒）检查一次
        if (event.side.isServer() && event.phase == TickEvent.Phase.END && 
            event.player instanceof ServerPlayer && event.player.tickCount % 20 == 0) {
            
            ServerPlayer player = (ServerPlayer) event.player;
            
            try {
                // 检查玩家是否已经获得过精灵之戒
                if (!hasReceivedRingOfTheElves(player)) {
                    // 检查玩家是否有任何精灵
                    if (hasAnySpirit(player)) {
                        // 给予精灵之戒
                        giveRingOfTheElves(player);
                        // 标记已经获得过精灵之戒
                        markRingOfTheElvesReceived(player);
                    }
                }
            } catch (Exception e) {
                // 记录错误但不崩溃游戏
                System.err.println("Error in ElfPrayerHandler: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查玩家是否已经获得过精灵之戒
     */
    private static boolean hasReceivedRingOfTheElves(Player player) {
        String playerUUID = player.getUUID().toString();

        // 首先检查玩家本地数据
        if (player.getPersistentData().getBoolean(NBT_KEY_RING_RECEIVED + "_" + playerUUID)) {
            return true;
        }

        // 然后检查服务器级别的数据
        if (player.getLevel() != null && !player.getLevel().isClientSide()) {
            try {
                var worldData = player.getLevel().getServer().overworld().getDataStorage();
                var savedData = worldData.computeIfAbsent(
                    (tag) -> new RingReceivedData(tag),
                    () -> new RingReceivedData(),
                    "tensuraenigmatic_ring_received"
                );

                if (savedData.hasPlayerReceived(playerUUID)) {
                    // 如果服务器数据显示已获得，同步到玩家本地数据
                    player.getPersistentData().putBoolean(NBT_KEY_RING_RECEIVED + "_" + playerUUID, true);
                    return true;
                }
            } catch (Exception e) {
                System.err.println("Error checking ring received data: " + e.getMessage());
            }
        }

        return false;
    }

    /**
     * 标记玩家已经获得过精灵之戒
     */
    private static void markRingOfTheElvesReceived(Player player) {
        // 使用玩家UUID作为唯一标识
        String playerUUID = player.getUUID().toString();
        player.getPersistentData().putBoolean(NBT_KEY_RING_RECEIVED + "_" + playerUUID, true);

        // 同时在服务器级别记录，防止数据丢失
        if (player.getLevel() != null && !player.getLevel().isClientSide()) {
            // 在世界数据中也记录一份
            var worldData = player.getLevel().getServer().overworld().getDataStorage();
            var savedData = worldData.computeIfAbsent(
                (tag) -> new RingReceivedData(tag),
                () -> new RingReceivedData(),
                "tensuraenigmatic_ring_received"
            );
            savedData.addPlayer(playerUUID);
            savedData.setDirty();
        }
    }
    
    /**
     * 检查玩家是否有任何精灵
     */
    private static boolean hasAnySpirit(Player player) {
        return TensuraSkillCapability.getFrom(player).map(cap -> {
            // 检查所有魔法元素的精灵等级
            for (MagicElemental elemental : MagicElemental.values()) {
                if (cap.getSpiritLevel(elemental.getId()) > 0) {
                    return true; // 找到至少一个精灵
                }
            }
            return false; // 没有找到任何精灵
        }).orElse(false);
    }
    
    /**
     * 给予玩家精灵之戒
     */
    private static void giveRingOfTheElves(ServerPlayer player) {
        ItemStack ring = new ItemStack(TensuraEnigmaticItems.RING_OF_THE_ELVES.get());
        
        // 尝试添加到玩家背包
        if (player.getInventory().add(ring)) {
            // 成功添加到背包
            player.sendSystemMessage(Component.translatable("message.tensuraenigmatic.elf_prayer.ring_received")
                .withStyle(ChatFormatting.GOLD));
        } else {
            // 背包满了，掉落在地上
            ItemEntity dropRing = new ItemEntity(player.level, player.getX(), player.getY(), player.getZ(), ring);
            player.level.addFreshEntity(dropRing);
            
            player.sendSystemMessage(Component.translatable("message.tensuraenigmatic.elf_prayer.ring_dropped")
                .withStyle(ChatFormatting.GOLD));
        }
        
        // 播放获得物品的声音
        player.playSound(net.minecraft.sounds.SoundEvents.PLAYER_LEVELUP, 1.0F, 1.0F);
    }
}
