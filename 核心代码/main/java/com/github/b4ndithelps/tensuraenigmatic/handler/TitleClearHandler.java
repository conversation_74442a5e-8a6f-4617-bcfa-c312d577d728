package com.github.b4ndithelps.tensuraenigmatic.handler;

import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 标题清除处理器 - 强制清除标题残留
 * Title Clear Handler - Forcefully clear title remnants
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class TitleClearHandler {
    
    private static boolean wasInWorld = false;
    private static int clearCounter = 0;
    
    /**
     * 客户端tick事件，检查并清除标题
     */
    @SubscribeEvent
    @OnlyIn(Dist.CLIENT)
    public static void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) {
            return;
        }
        
        try {
            Minecraft minecraft = Minecraft.getInstance();
            boolean currentlyInWorld = minecraft.level != null;
            
            // 如果从有世界变成没世界，开始清除计数
            if (wasInWorld && !currentlyInWorld) {
                clearCounter = 60; // 清除3秒钟
            }
            
            // 如果需要清除标题
            if (clearCounter > 0) {
                clearTitles(minecraft);
                clearCounter--;
            }
            
            wasInWorld = currentlyInWorld;
            
        } catch (Exception e) {
            // 记录错误但不崩溃
            System.err.println("Error in TitleClearHandler: " + e.getMessage());
        }
    }
    
    /**
     * 强制清除所有标题
     */
    private static void clearTitles(Minecraft minecraft) {
        if (minecraft.gui != null) {
            try {
                // 使用反射强制清除标题字段
                java.lang.reflect.Field titleField = minecraft.gui.getClass().getDeclaredField("title");
                titleField.setAccessible(true);
                titleField.set(minecraft.gui, null);
                
                java.lang.reflect.Field subtitleField = minecraft.gui.getClass().getDeclaredField("subtitle");
                subtitleField.setAccessible(true);
                subtitleField.set(minecraft.gui, null);
                
                java.lang.reflect.Field titleTimeField = minecraft.gui.getClass().getDeclaredField("titleTime");
                titleTimeField.setAccessible(true);
                titleTimeField.set(minecraft.gui, 0);
                
                java.lang.reflect.Field titleFadeInTimeField = minecraft.gui.getClass().getDeclaredField("titleFadeInTime");
                titleFadeInTimeField.setAccessible(true);
                titleFadeInTimeField.set(minecraft.gui, 0);
                
                java.lang.reflect.Field titleStayTimeField = minecraft.gui.getClass().getDeclaredField("titleStayTime");
                titleStayTimeField.setAccessible(true);
                titleStayTimeField.set(minecraft.gui, 0);
                
                java.lang.reflect.Field titleFadeOutTimeField = minecraft.gui.getClass().getDeclaredField("titleFadeOutTime");
                titleFadeOutTimeField.setAccessible(true);
                titleFadeOutTimeField.set(minecraft.gui, 0);
                
            } catch (Exception e) {
                // 如果反射失败，使用普通方法
                minecraft.gui.setTitle(Component.empty());
                minecraft.gui.setSubtitle(Component.empty());
                minecraft.gui.setTimes(0, 0, 0);
            }
        }
    }
}
