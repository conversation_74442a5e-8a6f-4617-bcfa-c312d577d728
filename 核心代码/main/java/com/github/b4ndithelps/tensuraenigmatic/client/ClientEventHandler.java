package com.github.b4ndithelps.tensuraenigmatic.client;

import com.github.b4ndithelps.tensuraenigmatic.TensuraEnigmatic;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;

/**
 * 客户端事件处理器
 * 处理客户端相关的初始化
 */
@Mod.EventBusSubscriber(modid = TensuraEnigmatic.MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
public class ClientEventHandler {
    
    @SubscribeEvent
    public static void onClientSetup(FMLClientSetupEvent event) {
        // 客户端初始化代码
        TensuraEnigmatic.LOGGER.info("Client setup completed for TensuraEnigmatic");
    }
}
