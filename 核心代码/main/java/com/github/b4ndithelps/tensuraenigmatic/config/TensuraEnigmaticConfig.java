package com.github.b4ndithelps.tensuraenigmatic.config;

import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.config.ModConfig;
public class TensuraEnigmaticConfig {
    
    public static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();
    public static final ForgeConfigSpec SPEC;
    
    // 魔源之戒配置 / Ring of Arcane Source Configuration
    public static final ForgeConfigSpec.IntValue RING_OF_ARCANE_SOURCE_MAGICULE_BONUS;
    public static final ForgeConfigSpec.DoubleValue RING_OF_ARCANE_SOURCE_DROP_CHANCE;
    
    // 精灵之戒配置 / Ring of the Elves Configuration
    public static final ForgeConfigSpec.IntValue RING_OF_THE_ELVES_ARMOR_BONUS;
    public static final ForgeConfigSpec.IntValue RING_OF_THE_ELVES_HEALTH_BONUS;
    public static final ForgeConfigSpec.IntValue RING_OF_THE_ELVES_MAGICULE_BONUS;
    public static final ForgeConfigSpec.IntValue RING_OF_THE_ELVES_PROTECTION_LEVEL;
    
    // 魔晶之戒配置 / Mana Ring Configuration
    public static final ForgeConfigSpec.IntValue MANA_RING_RECOVERY_AMOUNT;
    public static final ForgeConfigSpec.IntValue MANA_RING_RECOVERY_INTERVAL;
    

    
    static {
        BUILDER.comment(
            "Tensura: Enigmatic Legacy配置",
            "Tensura: Enigmatic Legacy Mod Configuration File"
        );
        
        // 魔源之戒配置
        BUILDER.push("ring_of_arcane_source");
        BUILDER.comment(
            "魔源之戒配置 - 提供魔素上限加成的戒指",
            "Ring of Arcane Source Configuration - Ring that provides magicule limit bonus"
        );
        
        RING_OF_ARCANE_SOURCE_MAGICULE_BONUS = BUILDER
            .comment(
                "魔源之戒提供的魔素上限加成",
                "Magicule limit bonus provided by Ring of Arcane Source",
                "默认值: 1000",
                "Default: 1000"
            )
            .defineInRange("magicule_bonus", 1000, 0, 100000);
            
        RING_OF_ARCANE_SOURCE_DROP_CHANCE = BUILDER
            .comment(
                "魔源之戒在地牢宝箱中的掉落概率 (0.0-1.0)",
                "Drop chance of Ring of Arcane Source in dungeon chests (0.0-1.0)",
                "默认值: 0.05 (5%)",
                "Default: 0.05 (5%)"
            )
            .defineInRange("drop_chance", 0.05, 0.0, 1.0);
        
        BUILDER.pop();
        
        // 精灵之戒配置
        BUILDER.push("ring_of_the_elves");
        BUILDER.comment(
            "精灵之戒配置 - 通过第一次精灵祈祷获得的强力戒指",
            "Ring of the Elves Configuration - Powerful ring obtained through first elf prayer"
        );
        
        RING_OF_THE_ELVES_ARMOR_BONUS = BUILDER
            .comment(
                "精灵之戒提供的防御力加成",
                "Armor bonus provided by Ring of the Elves",
                "默认值: 10",
                "Default: 10"
            )
            .defineInRange("armor_bonus", 10, 0, 100);
            
        RING_OF_THE_ELVES_HEALTH_BONUS = BUILDER
            .comment(
                "精灵之戒提供的血量加成",
                "Health bonus provided by Ring of the Elves",
                "默认值: 20",
                "Default: 20"
            )
            .defineInRange("health_bonus", 20, 0, 1000);
            
        RING_OF_THE_ELVES_MAGICULE_BONUS = BUILDER
            .comment(
                "精灵之戒提供的魔素上限加成",
                "Magicule limit bonus provided by Ring of the Elves",
                "默认值: 3000",
                "Default: 3000"
            )
            .defineInRange("magicule_bonus", 3000, 0, 100000);
            
        RING_OF_THE_ELVES_PROTECTION_LEVEL = BUILDER
            .comment(
                "精灵之戒自动附魔的保护等级",
                "Protection level automatically enchanted by Ring of the Elves",
                "默认值: 4",
                "Default: 4"
            )
            .defineInRange("protection_level", 4, 1, 10);
        
        BUILDER.pop();
        
        // 魔晶之戒配置
        BUILDER.push("mana_ring");
        BUILDER.comment(
            "魔晶之戒配置 - 提供魔素恢复的戒指",
            "Mana Ring Configuration - Ring that provides magicule recovery"
        );
        
        MANA_RING_RECOVERY_AMOUNT = BUILDER
            .comment(
                "魔晶之戒每次恢复的魔素数量",
                "Amount of magicule recovered by Mana Ring per tick",
                "默认值: 70",
                "Default: 70"
            )
            .defineInRange("recovery_amount", 70, 1, 1000);
            
        MANA_RING_RECOVERY_INTERVAL = BUILDER
            .comment(
                "魔晶之戒恢复魔素的间隔 (tick)",
                "Interval for Mana Ring magicule recovery (ticks)",
                "默认值: 20 (1秒)",
                "Default: 20 (1 second)"
            )
            .defineInRange("recovery_interval", 20, 1, 200);
        
        BUILDER.pop();
        
        SPEC = BUILDER.build();
    }
    
    /**
     * 注册配置
     * Register configuration
     */
    public static void register() {
        ModLoadingContext.get().registerConfig(ModConfig.Type.COMMON, SPEC, "tensura-enigmatic.toml");
    }
}
