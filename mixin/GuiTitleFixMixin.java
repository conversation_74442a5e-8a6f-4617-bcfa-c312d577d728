package com.github.b4ndithelps.tensuraenigmatic.mixin;

import net.minecraft.client.gui.Gui;
import net.minecraft.network.chat.Component;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;

@Mixin({Gui.class})
public class GuiTitleFixMixin {
   @Shadow
   private Component f_93001_;
   @Shadow
   private Component f_93002_;
   @Shadow
   private int f_92970_;
   @Shadow
   private int f_92971_;
   @Shadow
   private int f_92972_;
   @Shadow
   private int f_93000_;
   private boolean shouldClearTitle = false;

   public void clearAllTitles() {
      this.f_93001_ = null;
      this.f_93002_ = null;
      this.f_92970_ = 0;
      this.f_92971_ = 0;
      this.f_92972_ = 0;
      this.f_93000_ = 0;
      this.shouldClearTitle = true;
   }
}
