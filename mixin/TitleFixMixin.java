package com.github.b4ndithelps.tensuraenigmatic.mixin;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin({Minecraft.class})
public class TitleFixMixin {
   @Shadow
   private volatile boolean f_91019_;

   @Inject(
      method = {"clearLevel()V"},
      at = {@At("HEAD")}
   )
   private void clearTitleOnClearLevel(CallbackInfo ci) {
      this.clearTitleOverlay();
   }

   @Inject(
      method = {"clearLevel(Lnet/minecraft/client/gui/screens/Screen;)V"},
      at = {@At("HEAD")}
   )
   private void clearTitleOnClearLevelWithScreen(Screen screen, CallbackInfo ci) {
      this.clearTitleOverlay();
   }

   private void clearTitleOverlay() {
      try {
         Minecraft minecraft = (Minecraft)this;
         if (minecraft.f_91065_ != null) {
            ((GuiTitleFixMixin)minecraft.f_91065_).clearAllTitles();
            minecraft.f_91065_.m_168714_(Component.m_237119_());
            minecraft.f_91065_.m_168711_(Component.m_237119_());
            minecraft.f_91065_.m_168684_(0, 0, 0);
         }
      } catch (Exception var2) {
         System.err.println("Error in TitleFixMixin: " + var2.getMessage());
      }

   }
}
